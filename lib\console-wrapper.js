/**
 * <PERSON><PERSON><PERSON> Console Wrapper
 * Provides environment-aware console logging
 * Automatically disables console output in production builds
 */

(function() {
    'use strict';

    // Detect environment automatically
    // Production mode enabled for dist folder
    const isProduction = true; // Production build

    // Store original console methods
    const originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error,
        info: console.info,
        debug: console.debug,
        trace: console.trace,
        time: console.time,
        timeEnd: console.timeEnd,
        group: console.group,
        groupEnd: console.groupEnd,
        count: console.count,
        clear: console.clear,
        table: console.table,
        assert: console.assert
    };

    // Create no-op functions for production
    const noOp = () => {};

    // Create the Stashy console wrapper
    const StashyConsole = {
        // Core logging methods
        log: isProduction ? noOp : originalConsole.log.bind(console),
        warn: isProduction ? noOp : originalConsole.warn.bind(console),
        error: isProduction ? originalConsole.error.bind(console) : originalConsole.error.bind(console), // Keep errors even in production
        info: isProduction ? noOp : originalConsole.info.bind(console),
        debug: isProduction ? noOp : originalConsole.debug.bind(console),
        trace: isProduction ? noOp : originalConsole.trace.bind(console),
        
        // Timing methods
        time: isProduction ? noOp : originalConsole.time.bind(console),
        timeEnd: isProduction ? noOp : originalConsole.timeEnd.bind(console),
        
        // Grouping methods
        group: isProduction ? noOp : originalConsole.group.bind(console),
        groupEnd: isProduction ? noOp : originalConsole.groupEnd.bind(console),
        
        // Other methods
        count: isProduction ? noOp : originalConsole.count.bind(console),
        clear: isProduction ? noOp : originalConsole.clear.bind(console),
        table: isProduction ? noOp : originalConsole.table.bind(console),
        assert: isProduction ? noOp : originalConsole.assert.bind(console),

        // Utility methods
        isProduction: () => isProduction,
        isDevelopment: () => !isProduction,
        
        // Conditional logging
        devLog: function(...args) {
            if (!isProduction) {
                originalConsole.log.apply(console, ['[DEV]', ...args]);
            }
        },
        
        prodLog: function(...args) {
            if (isProduction) {
                originalConsole.log.apply(console, ['[PROD]', ...args]);
            }
        },

        // Force logging (bypasses production check)
        forceLog: originalConsole.log.bind(console),
        forceWarn: originalConsole.warn.bind(console),
        forceError: originalConsole.error.bind(console),

        // Restore original console (for debugging)
        restore: function() {
            Object.assign(console, originalConsole);
        }
    };

    // Option 1: Replace global console (aggressive approach)
    if (isProduction) {
        // In production, replace console methods with no-ops (except error)
        console.log = noOp;
        console.warn = noOp;
        console.info = noOp;
        console.debug = noOp;
        console.trace = noOp;
        console.time = noOp;
        console.timeEnd = noOp;
        console.group = noOp;
        console.groupEnd = noOp;
        console.count = noOp;
        console.clear = noOp;
        console.table = noOp;
        console.assert = noOp;
        // Keep console.error for critical issues
    }

    // Option 2: Provide Stashy-specific console (recommended approach)
    window.StashyConsole = StashyConsole;
    
    // Option 3: Create a global debug function
    window.debugLog = StashyConsole.devLog;
    
    // Silent initialization - no console output in production

})();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.StashyConsole;
}
