{"manifest_version": 3, "name": "<PERSON><PERSON><PERSON>", "version": "1.1", "description": "Comprehensive note-taking extension. Transform any webpage into your personal knowledge base.", "permissions": ["storage", "unlimitedStorage", "activeTab", "tabs", "alarms", "notifications", "downloads", "offscreen", "contextMenus", "identity", "scripting", "debugger"], "host_permissions": ["*://*.googleapis.com/*", "*://*.google.com/oauth2/*"], "author": {"email": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gamil.com"}, "oauth2": {"client_id": "236808166220-2iult4m2hajen77di2ai0j5dfmck0tvg.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/calendar.events", "https://www.googleapis.com/auth/documents", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"]}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'none'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://* file://*; connect-src 'self' https://*.googleapis.com https://www.googleapis.com https://oauth2.googleapis.com https://api.openai.com https://api.anthropic.com https://generativelanguage.googleapis.com https://api.cohere.ai https://api-inference.huggingface.co; frame-src 'none'; base-uri 'self'; worker-src 'self'"}, "background": {"service_worker": "background.js"}, "icons": {"16": "icon16.png", "32": "icon32.png", "48": "icon48.png", "128": "icon128.png"}, "web_accessible_resources": [{"resources": ["icon128.png", "icon48.png", "lib/console-wrapper.js", "workers/storage-worker.js", "workers/image-worker.js", "workers/data-worker.js", "privacy-policy.html", "privacy-policy.css", "permissions-explanation.html", "permissions-explanation.css", "activation-guide.html", "data-deletion.html", "data-deletion.css", "help.html", "help.css", "lib/google-docs-integration.js", "lib/google-ai-integration.js", "lib/ai-provider-detector.js", "lib/universal-ai-adapter.js", "lib/ai-migration-bridge.js", "lib/ai-enhancement-utilities.js", "lib/secure-api-storage.js", "lib/ai-security-manager.js", "lib/readability.js", "content/content-readability.js", "ui/dashboard-events.js", "ui/google-docs-export.js", "ui/dashboard-gdocs-integration.js", "ui/direct-gdocs-button.js", "content/content-gdocs-button.js", "content/content-ai-features.js", "content/content-academic-solver.js", "content/content-transcript.js", "ui/google-docs-export.css", "standalone-gdocs-button.js"], "matches": ["*://*/*"]}]}